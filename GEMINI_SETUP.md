# 🤖 Gemini 2.0 API 集成指南

本指南将帮助您配置和使用Google Gemini 2.0 API来增强游戏脚本的AI决策能力。

## 🎯 **Gemini的优势**

- 🧠 **强大的推理能力**: Gemini 2.0在复杂策略分析方面表现出色
- 🌍 **多语言支持**: 完美支持中英文混合对话
- ⚡ **快速响应**: 比GPT-4更快的响应速度
- 💰 **成本效益**: 相比GPT-4更具性价比
- 🎮 **游戏优化**: 对游戏策略分析有很好的理解能力

## 📋 **配置步骤**

### 1. 获取Gemini API密钥

1. 访问 [Google AI Studio](https://aistudio.google.com/)
2. 登录您的Google账户
3. 点击 "Get API Key" 
4. 创建新的API密钥
5. 复制API密钥（格式类似：`AIzaSyA...`）

### 2. 配置config.yaml

在您的 `config.yaml` 文件中添加Gemini配置：

```yaml
GEMINI:
    # API密钥列表。机器人将为每条消息随机使用一个API密钥。
    API_KEYS: ["AIzaSyA_your_actual_gemini_api_key_here"]
    # 要使用的Gemini模型。推荐使用 gemini-2.0-flash-exp
    MODEL: "gemini-2.0-flash-exp"
    
    # 在某些国家可能需要使用代理访问Gemini。
    # 默认值为空。格式：用户名:密码@IP:端口
    PROXY_FOR_GEMINI: ""
```

### 3. 可用的Gemini模型

- `gemini-2.0-flash-exp` - **推荐**，最新的实验版本，性能最佳
- `gemini-1.5-pro` - 稳定版本，适合生产环境
- `gemini-1.5-flash` - 快速版本，响应更快但能力稍弱

## 🔧 **AI优先级系统**

脚本现在支持多个AI服务，按以下优先级自动选择：

1. **🥇 Gemini** - 首选，如果配置了有效的API密钥
2. **🥈 DeepSeek** - 备选，成本低廉的中文AI
3. **🥉 ChatGPT** - 最后选择，功能强大但成本较高

### 配置示例

```yaml
# 只使用Gemini
GEMINI:
    API_KEYS: ["your-gemini-key"]
    MODEL: "gemini-2.0-flash-exp"
DEEPSEEK:
    API_KEYS: [""]  # 留空
CHAT_GPT:
    API_KEYS: [""]  # 留空

# 或者配置多个作为备份
GEMINI:
    API_KEYS: ["gemini-key-1", "gemini-key-2"]
    MODEL: "gemini-2.0-flash-exp"
DEEPSEEK:
    API_KEYS: ["deepseek-backup-key"]
    MODEL: "deepseek-chat"
CHAT_GPT:
    API_KEYS: ["gpt-backup-key"]
    MODEL: "gpt-4o"
```

## 🧪 **测试配置**

运行测试脚本验证配置：

```bash
python test_gemini.py
```

测试脚本会：
- ✅ 检查配置文件是否正确加载
- ✅ 验证API密钥格式
- ✅ 测试与Gemini API的连接
- ✅ 验证游戏策略响应格式

## 🎮 **游戏中的表现**

Gemini在游戏中的AI决策示例：

### 战斗策略
```
输入: 玩家血量=45/100, 敌人血量=25/80, 可用招式=[rock: Charges=2, scissor: Charges=3]

Gemini输出: "建议出scissor，因为敌人血量较低(25/80)且scissor攻击力最高(18)，虽然被rock克制但敌人护盾为0可直接造成伤害，优先快速结束战斗"
```

### 战利品选择
```
输入: 当前血量接近满值，可选战利品=[增加生命值, 增加攻击力, 增加防御力]

Gemini输出: "建议出增加攻击力，因为当前血量已接近满值，增加生命值收益较低，而攻击力提升能在后续战斗中带来更大优势"
```

## 🔍 **故障排除**

### 常见问题

1. **API密钥无效**
   ```
   错误: Gemini API密钥无效
   解决: 检查API密钥是否正确复制，确保没有多余的空格
   ```

2. **配额超限**
   ```
   错误: Gemini API配额超限
   解决: 检查Google Cloud Console中的配额设置，或等待配额重置
   ```

3. **网络连接问题**
   ```
   错误: 连接超时
   解决: 检查网络连接，考虑配置代理
   ```

### 代理配置

如果需要使用代理访问Gemini：

```yaml
GEMINI:
    PROXY_FOR_GEMINI: "*********************************:port"
    # 或者
    PROXY_FOR_GEMINI: "socks5://username:password@proxy_ip:port"
```

## 📊 **性能对比**

| AI服务 | 响应速度 | 策略质量 | 成本 | 中文支持 |
|--------|----------|----------|------|----------|
| Gemini 2.0 | ⚡⚡⚡ | 🧠🧠🧠🧠 | 💰💰 | 🇨🇳🇨🇳🇨🇳 |
| DeepSeek | ⚡⚡ | 🧠🧠🧠 | 💰 | 🇨🇳🇨🇳🇨🇳🇨🇳 |
| ChatGPT | ⚡⚡ | 🧠🧠🧠🧠🧠 | 💰💰💰 | 🇨🇳🇨🇳 |

## 🚀 **开始使用**

1. 按照上述步骤配置API密钥
2. 运行测试脚本验证配置
3. 启动游戏脚本：`python main.py`
4. 选择 "AI Giga" 任务
5. 享受Gemini驱动的智能游戏体验！

## 💡 **提示**

- 建议配置多个API密钥以提高稳定性
- 可以同时配置多个AI服务作为备份
- 定期检查API使用量和余额
- 如果遇到问题，查看日志中的详细错误信息

---

**祝您游戏愉快！** 🎮✨
