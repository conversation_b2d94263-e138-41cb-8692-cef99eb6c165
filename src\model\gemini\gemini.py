from loguru import logger
import httpx
import json
from typing import <PERSON>ple


async def ask_gemini(api_key: str, model: str, user_message: str, prompt: str, proxy: str = "") -> Tuple[bool, str]:
    """
    向Google Gemini 2.0模型发送消息并获取响应，支持SOCKS5和HTTP代理。

    Args:
        api_key (str): Google Gemini API密钥
        model (str): 模型名称 (如 "gemini-2.0-flash-exp")
        user_message (str): 发送给Gemini的消息
        prompt (str): 系统提示词
        proxy (str): 代理地址，格式为 [protocol://][user:pass@]ip:port
                   例如: socks5://user:pass@ip:port 或 http://ip:port

    Returns:
        Tuple[bool, str]: (是否成功, 响应消息)
    """
    client_params = {}

    if proxy:
        logger.info(f"使用代理: {proxy} 连接Gemini")
        # 检测代理协议类型
        if not proxy.startswith(("http://", "https://", "socks5://", "socks5h://")):
            # 默认添加 http:// 前缀（向后兼容）
            proxy = f"http://{proxy}"

        # 配置代理字典
        client_params["proxies"] = {
            "http://": proxy,
            "https://": proxy
        }

    try:
        async with httpx.AsyncClient(**client_params) as http_client:
            return await _make_request(http_client, api_key, model, user_message, prompt)
    except Exception as e:
        logger.error(f"代理配置错误: {str(e)}")
        return False, f"代理配置错误: {str(e)}"


async def _make_request(http_client: httpx.AsyncClient, api_key: str, model: str, user_message: str, prompt: str) -> Tuple[bool, str]:
    """发送请求到Gemini API"""
    
    # Gemini API URL
    url = f"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent"
    
    # 准备请求头
    headers = {
        "Content-Type": "application/json",
        "x-goog-api-key": api_key
    }

    # 构建消息内容
    # 如果有系统提示词，将其与用户消息合并
    if prompt:
        combined_message = f"{prompt}\n\n用户输入: {user_message}"
    else:
        combined_message = user_message

    # 构建请求数据
    data = {
        "contents": [
            {
                "parts": [
                    {
                        "text": combined_message
                    }
                ]
            }
        ],
        "generationConfig": {
            "temperature": 0.7,
            "maxOutputTokens": 1000,
            "topP": 0.8,
            "topK": 10
        }
    }

    try:
        # 发送API请求
        response = await http_client.post(
            url,
            headers=headers,
            json=data,
            timeout=300.0
        )

        if response.status_code == 200:
            response_data = response.json()
            
            # 检查响应格式
            if "candidates" in response_data and len(response_data["candidates"]) > 0:
                candidate = response_data["candidates"][0]
                if "content" in candidate and "parts" in candidate["content"]:
                    response_text = candidate["content"]["parts"][0]["text"]
                    return True, response_text
                else:
                    return False, "Gemini API返回格式异常：缺少content或parts"
            else:
                return False, "Gemini API返回格式异常：缺少candidates"
        else:
            error_message = response.text
            if "quota" in error_message.lower() or "exceeded" in error_message.lower():
                return False, "Gemini API密钥余额不足或配额超限"
            elif "invalid" in error_message.lower() and "key" in error_message.lower():
                return False, "Gemini API密钥无效"
            else:
                logger.error(f"Gemini API错误 {response.status_code}: {error_message}")
                return False, f"Gemini API错误: {error_message}"

    except httpx.TimeoutException:
        return False, "Gemini API请求超时"
    except json.JSONDecodeError:
        return False, "Gemini API返回数据格式错误"
    except Exception as e:
        error_str = str(e).lower()
        if "quota" in error_str or "exceeded" in error_str:
            return False, "Gemini API配额超限，请稍后重试"
        elif "invalid" in error_str and "key" in error_str:
            return False, "Gemini API密钥无效"
        else:
            logger.error(f"Gemini错误: {str(e)}")
            return False, f"Gemini错误: {str(e)}"


# 使用示例
if __name__ == "__main__":
    import asyncio

    async def test():
        # 测试不同类型的代理
        api_key = "your_gemini_api_key"
        model = "gemini-2.0-flash-exp"
        message = "Hello! 请用中文回复。"
        prompt = "你是一个智能游戏助手"

        # 测试 HTTP 代理
        result = await ask_gemini(api_key, model, message, prompt, "*******************************")
        print("HTTP Proxy:", result)

        # 测试 SOCKS5 代理
        result = await ask_gemini(api_key, model, message, prompt, "socks5://user:pass@127.0.0.1:1080")
        print("SOCKS5 Proxy:", result)

        # 测试无代理
        result = await ask_gemini(api_key, model, message, prompt)
        print("No Proxy:", result)

    asyncio.run(test())
