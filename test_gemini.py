#!/usr/bin/env python3
"""
测试Gemini API集成的脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.model.gemini.gemini import ask_gemini


async def test_gemini():
    """测试Gemini API"""
    print("🧪 测试Gemini API集成...")
    
    # 测试配置
    api_key = "your-gemini-api-key-here"  # 请替换为您的实际API密钥
    model = "gemini-2.0-flash-exp"
    
    # 测试消息
    prompt = """你是一个智能游戏助手，帮助玩家在基于"石头、布、剪刀"规则的dungeon游戏中选择最佳出招。

### 当前游戏规则：
1. **克制关系**：
   - 石头（rock）克剪刀（scissor），剪刀克布（paper），布克石头，相同招式为平局。
   - 克制胜：你的攻击直接扣敌方血量/护盾，敌方伤害减为0。

2. **伤害计算**：
   - 你的伤害 = 你的ATK - 敌方DEF（最小为0）。
   - 敌方伤害 = 敌方ATK - 你的DEF（最小为0）。

### 返回格式：
- 出招建议：`建议出<选择>，因为<理由>`
  - 示例：`建议出paper，因为生存性高，对石头胜无伤，对布平低伤，对剪刀仅受1伤，且rock Charges为1不可用`
"""
    
    user_message = """玩家: 血量=45/100, 护盾=10, 招式=[rock: ATK=15, DEF=8, Charges=2, paper: ATK=12, DEF=12, Charges=1, scissor: ATK=18, DEF=6, Charges=3], 敌人: 血量=25/80, 护盾=0, 上次出招=rock

请分析当前局势并给出最佳出招建议。注意：强制不使用`currentCharges`为1的招式，只从`currentCharges` > 1的招式中选择。"""
    
    try:
        print(f"📡 发送请求到Gemini API...")
        print(f"🤖 模型: {model}")
        print(f"💬 用户消息: {user_message[:100]}...")
        
        success, response = await ask_gemini(
            api_key=api_key,
            model=model,
            user_message=user_message,
            prompt=prompt
        )
        
        if success:
            print("✅ Gemini API调用成功!")
            print(f"🎯 AI响应: {response}")
            
            # 检查响应格式
            if "建议出" in response:
                print("✅ 响应格式正确，包含游戏建议")
            else:
                print("⚠️ 响应格式可能需要调整")
                
        else:
            print(f"❌ Gemini API调用失败: {response}")
            
    except Exception as e:
        print(f"💥 测试过程中发生错误: {str(e)}")


async def test_config_loading():
    """测试配置文件加载"""
    print("\n🔧 测试配置文件加载...")
    
    try:
        from src.utils.config import get_config
        config = get_config()
        
        print("✅ 配置文件加载成功!")
        print(f"🔑 Gemini API Keys: {len(config.GEMINI.API_KEYS)} 个")
        print(f"🤖 Gemini Model: {config.GEMINI.MODEL}")
        print(f"🌐 Gemini Proxy: {config.GEMINI.PROXY_FOR_GEMINI or '无'}")
        
        if config.GEMINI.API_KEYS and config.GEMINI.API_KEYS[0]:
            masked_key = config.GEMINI.API_KEYS[0][:10] + "..." if len(config.GEMINI.API_KEYS[0]) > 10 else "空"
            print(f"🔐 第一个API Key: {masked_key}")
        else:
            print("⚠️ 未配置Gemini API Key")
            
    except Exception as e:
        print(f"❌ 配置文件加载失败: {str(e)}")


def main():
    """主函数"""
    print("🚀 Gemini API集成测试")
    print("=" * 50)
    
    # 运行测试
    asyncio.run(test_config_loading())
    
    print("\n" + "=" * 50)
    print("📝 使用说明:")
    print("1. 请在config.yaml中配置您的Gemini API密钥")
    print("2. 修改test_gemini.py中的api_key变量")
    print("3. 运行: python test_gemini.py")
    print("4. 如果测试成功，您就可以在游戏中使用Gemini了!")
    
    # 如果用户想要测试实际API调用，取消下面的注释
    # print("\n" + "=" * 50)
    # asyncio.run(test_gemini())


if __name__ == "__main__":
    main()
